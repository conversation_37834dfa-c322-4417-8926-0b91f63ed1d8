# 🗺️ Movimento Automático do Mapa - Nova Funcionalidade

## 🎯 Objetivo

Implementar movimento automático do mapa no Google Maps para carregar novos leads continuamente quando não encontrar mais resultados na área atual.

## 🚀 Funcionalidade Implementada

### **Movimento Automático Inteligente**

Quando o scraper não consegue mais carregar novos leads através da rolagem, ele automaticamente:

1. **Move o mapa** em diferentes direções (Norte, Sul, Leste, Oeste, diagonais)
2. **Verifica se novos leads** apareceram após cada movimento
3. **Continua a extração** com os novos leads encontrados
4. **Repete o processo** até atingir o número desejado de leads

## 🔧 Como Funciona

### **Sequência de Ações:**

1. **Detecção de Fim de Resultados**
   - Scraper tenta rolar a página 3 vezes sem sucesso
   - Ativa automaticamente o movimento do mapa

2. **Movimento Automático**
   - Tenta mover o mapa para **Leste** (direita)
   - Se não carregar novos leads, tenta **Sul** (baixo)
   - Continua com **Oeste** (esquerda), **Norte** (cima)
   - Testa movimentos diagonais se necessário

3. **Verificação de Novos Leads**
   - Após cada movimento, conta os elementos na página
   - Se encontrar novos leads, continua a extração
   - Se não encontrar, tenta próxima direção

4. **Continuação da Extração**
   - Retorna ao processo normal de extração
   - Processa os novos leads encontrados
   - Repete o ciclo quando necessário

## 📊 Logs da Nova Funcionalidade

### **Logs Esperados:**

```
2025-06-04 01:27:28 - Prospector - INFO - 🔄 Tentando estratégias alternativas para carregar mais resultados...
2025-06-04 01:27:28 - Prospector - INFO - 🗺️ Tentativa 1: Movimento automático do mapa
2025-06-04 01:27:28 - Prospector - INFO - 🗺️ Movendo mapa automaticamente para carregar novos leads (direção: random)
2025-06-04 01:27:28 - Prospector - INFO - Elementos antes do movimento: 25
2025-06-04 01:27:28 - Prospector - INFO - Mapa encontrado: //div[@id="map"]
2025-06-04 01:27:30 - Prospector - INFO - Executando movimento 1: (300, 0)
2025-06-04 01:27:35 - Prospector - INFO - Elementos após movimento 1: 30
2025-06-04 01:27:35 - Prospector - INFO - ✅ Movimento bem-sucedido! Carregados 5 novos elementos
2025-06-04 01:27:35 - Prospector - INFO - ✅ Movimento automático do mapa carregou novos leads!
```

## 🎮 Direções de Movimento

### **Direções Disponíveis:**

- **`"leste"`** - Move para a direita (300px, 0px)
- **`"oeste"`** - Move para a esquerda (-300px, 0px)  
- **`"norte"`** - Move para cima (0px, -300px)
- **`"sul"`** - Move para baixo (0px, 300px)
- **`"nordeste"`** - Diagonal direita-cima (200px, -200px)
- **`"noroeste"`** - Diagonal esquerda-cima (-200px, -200px)
- **`"sudeste"`** - Diagonal direita-baixo (200px, 200px)
- **`"sudoeste"`** - Diagonal esquerda-baixo (-200px, 200px)
- **`"random"`** - Sequência automática de movimentos

### **Sequência Padrão (Random):**
1. Leste (direita)
2. Sul (baixo)  
3. Oeste (esquerda)
4. Norte (cima)

## 🧪 Como Testar

### **Teste Automático:**
```bash
python teste_movimento_mapa.py
```

### **Teste Manual:**
1. Execute o scraper normalmente
2. Aguarde até ele extrair todos os leads visíveis
3. Observe os logs para ver o movimento automático em ação
4. Verifique se novos leads são carregados

## 📈 Benefícios da Nova Funcionalidade

### **1. Extração Contínua**
- ✅ Não para quando acaba a rolagem
- ✅ Explora áreas adjacentes automaticamente
- ✅ Maximiza o número de leads extraídos

### **2. Cobertura Geográfica Ampliada**
- ✅ Explora bairros vizinhos
- ✅ Encontra estabelecimentos em ruas próximas
- ✅ Aumenta a diversidade de leads

### **3. Automação Completa**
- ✅ Não requer intervenção manual
- ✅ Funciona automaticamente quando necessário
- ✅ Logs detalhados para monitoramento

### **4. Inteligência Adaptativa**
- ✅ Para quando não encontra mais leads
- ✅ Tenta diferentes direções automaticamente
- ✅ Otimiza o tempo de extração

## 🔍 Exemplo Prático

### **Cenário:**
- Busca: "tecnologia" no "centro, São Paulo (SP)"
- Leads solicitados: 50
- Leads encontrados inicialmente: 25

### **Processo:**
1. **Extrai 25 leads** da área inicial
2. **Tenta rolar** para carregar mais (falha 3 vezes)
3. **Ativa movimento automático**:
   - Move para **Leste**: encontra 8 novos leads
   - Extrai os 8 novos leads (total: 33)
   - Move para **Sul**: encontra 12 novos leads  
   - Extrai os 12 novos leads (total: 45)
   - Move para **Oeste**: encontra 5 novos leads
   - Extrai os 5 novos leads (total: 50)
4. **Completa a extração** com 50 leads

## ⚙️ Configurações Técnicas

### **Parâmetros de Movimento:**
- **Distância padrão**: 300 pixels
- **Tempo de espera**: 3 segundos após movimento
- **Verificação**: 2 segundos para contagem de elementos
- **Máximo de tentativas**: 6 direções diferentes

### **Seletores do Mapa:**
```python
mapa_seletores = [
    '//div[@id="map"]',
    '//div[contains(@class, "widget-scene")]',
    '//canvas[contains(@class, "widget-scene-canvas")]',
    '//div[@role="application"]',
    '//div[contains(@jsaction, "mousedown")]',
    '//div[contains(@class, "maps-embed")]'
]
```

## 🛡️ Tratamento de Erros

### **Cenários Tratados:**
- ❌ **Mapa não encontrado**: Tenta múltiplos seletores
- ❌ **Movimento falha**: Continua com próxima direção
- ❌ **Elementos não carregam**: Para após todas as tentativas
- ❌ **Erro crítico**: Logs detalhados e continuação segura

## 📋 Integração com Sistema Existente

### **Pontos de Integração:**

1. **`extrair_clientes()`** - Chama movimento quando rolagem falha
2. **`tentar_carregar_mais_resultados()`** - Inclui movimento como primeira estratégia
3. **`mover_mapa_automaticamente()`** - Nova função principal
4. **Logs existentes** - Integrados com sistema de logging atual

### **Compatibilidade:**
- ✅ Funciona com todas as melhorias anteriores
- ✅ Mantém compatibilidade com código existente
- ✅ Não afeta funcionamento normal do scraper
- ✅ Ativa apenas quando necessário

## 🎯 Resultados Esperados

### **Antes da Funcionalidade:**
- Extraía 10-25 leads e parava
- Limitado à área inicial de busca
- Requeria intervenção manual para continuar

### **Depois da Funcionalidade:**
- Extrai 50-100+ leads automaticamente
- Explora múltiplas áreas geograficamente
- Funciona completamente autônomo
- Maximiza o retorno de cada busca

## 🚀 Próximos Passos

1. **Monitorar logs** para otimizar distâncias de movimento
2. **Ajustar sequência** de direções baseado em resultados
3. **Implementar zoom automático** para áreas muito densas
4. **Adicionar limite geográfico** para evitar áreas muito distantes

A nova funcionalidade transforma o scraper de uma ferramenta limitada em um sistema de extração contínua e inteligente, maximizando significativamente o número de leads capturados por execução.
