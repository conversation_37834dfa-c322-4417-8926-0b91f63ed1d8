#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do Movimento Automático do Mapa
Verifica se as melhorias implementadas estão funcionando corretamente
"""

import sys
import os
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options

# Adicionar o diretório atual ao path para importar logic_bot
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logic_bot

# Configurar logging para ver os detalhes
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('teste_movimento.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def callback_progresso(atual, total, cliente):
    """Callback para mostrar progresso"""
    porcentagem = (atual / total) * 100
    print(f"Progresso: {atual}/{total} ({porcentagem:.1f}%) - {cliente['nome']}")

def teste_movimento_automatico():
    """
    Testa o movimento automático do mapa com uma busca real
    """
    driver = None
    try:
        logger.info("🚀 Iniciando teste do movimento automático do mapa...")
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        # chrome_options.add_argument("--headless")  # Comentar para ver o navegador
        
        # Inicializar driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Abrir Google Maps
        logger.info("📍 Abrindo Google Maps...")
        driver.get('https://www.google.com/maps/')
        time.sleep(3)
        
        # Teste 1: Buscar localização
        logger.info("🔍 Teste 1: Buscando localização...")
        localizacao = "CENTRO, Rio de Janeiro (RJ)"
        if logic_bot.buscar_localizacao(driver, localizacao):
            logger.info("✅ Localização encontrada com sucesso!")
        else:
            logger.error("❌ Falha ao buscar localização")
            return False
        
        time.sleep(2)
        
        # Teste 2: Buscar palavra-chave
        logger.info("🔍 Teste 2: Buscando palavra-chave...")
        palavra_chave = "PSICOLOGIA"
        if logic_bot.buscar_palavra_chave(driver, palavra_chave):
            logger.info("✅ Palavra-chave encontrada com sucesso!")
        else:
            logger.error("❌ Falha ao buscar palavra-chave")
            return False
        
        time.sleep(2)
        
        # Teste 3: Extrair clientes com movimento automático
        logger.info("🔍 Teste 3: Extraindo clientes com movimento automático...")
        quantidade_desejada = 50  # Quantidade que deve forçar o movimento do mapa
        
        clientes = logic_bot.extrair_clientes(driver, quantidade_desejada, callback_progresso)
        
        # Verificar resultados
        total_extraidos = len(clientes)
        logger.info(f"📊 Resultados do teste:")
        logger.info(f"   - Leads solicitados: {quantidade_desejada}")
        logger.info(f"   - Leads extraídos: {total_extraidos}")
        logger.info(f"   - Taxa de sucesso: {(total_extraidos/quantidade_desejada)*100:.1f}%")
        
        if total_extraidos > 10:  # Se conseguiu mais de 10, provavelmente o movimento funcionou
            logger.info("✅ TESTE PASSOU! O movimento automático do mapa está funcionando!")
            return True
        else:
            logger.warning("⚠️ TESTE PARCIAL! Poucos leads extraídos, pode ser que o movimento não tenha funcionado completamente.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro durante o teste: {str(e)}")
        return False
        
    finally:
        if driver:
            logger.info("🔚 Fechando navegador...")
            driver.quit()

def teste_movimento_mapa_isolado():
    """
    Testa apenas a função de movimento do mapa
    """
    driver = None
    try:
        logger.info("🗺️ Testando função de movimento do mapa isoladamente...")
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Abrir Google Maps com uma busca
        driver.get('https://www.google.com/maps/search/restaurante+centro+rio+de+janeiro/')
        time.sleep(5)
        
        # Testar movimento em diferentes direções
        direcoes = ["norte", "sul", "leste", "oeste", "random"]
        
        for direcao in direcoes:
            logger.info(f"🗺️ Testando movimento para {direcao}...")
            resultado = logic_bot.mover_mapa_automaticamente(driver, direcao)
            if resultado:
                logger.info(f"✅ Movimento para {direcao} funcionou!")
            else:
                logger.warning(f"⚠️ Movimento para {direcao} não carregou novos elementos")
            time.sleep(2)
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste de movimento: {str(e)}")
        return False
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    print("🧪 TESTE DO MOVIMENTO AUTOMÁTICO DO MAPA")
    print("=" * 50)
    
    # Executar testes
    print("\n1️⃣ Testando movimento do mapa isoladamente...")
    teste1 = teste_movimento_mapa_isolado()
    
    print("\n2️⃣ Testando extração completa com movimento automático...")
    teste2 = teste_movimento_automatico()
    
    # Resultados finais
    print("\n" + "=" * 50)
    print("📋 RESULTADOS DOS TESTES:")
    print(f"   - Teste de movimento isolado: {'✅ PASSOU' if teste1 else '❌ FALHOU'}")
    print(f"   - Teste de extração completa: {'✅ PASSOU' if teste2 else '❌ FALHOU'}")
    
    if teste1 and teste2:
        print("\n🎉 TODOS OS TESTES PASSARAM! O movimento automático está funcionando!")
    else:
        print("\n⚠️ Alguns testes falharam. Verifique os logs para mais detalhes.")
    
    print("\n📄 Logs salvos em: teste_movimento.log")
