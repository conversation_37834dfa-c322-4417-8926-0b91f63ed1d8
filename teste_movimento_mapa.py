#!/usr/bin/env python3
"""
Script de teste para validar a funcionalidade de movimento automático do mapa.
Testa se o scraper consegue mover o mapa automaticamente para carregar novos leads.
"""

import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

# Importar as funções melhoradas
import logic_bot

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - TESTE_MOVIMENTO - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('teste_movimento_mapa.log')
    ]
)

logger = logging.getLogger('TesteMovimento')

def configurar_driver():
    """Configura driver para teste"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logger.info("✅ Driver configurado")
        return driver
    except Exception as e:
        logger.error(f"❌ Erro ao configurar driver: {str(e)}")
        return None

def testar_movimento_automatico():
    """Testa a funcionalidade de movimento automático do mapa"""
    logger.info("🚀 TESTANDO MOVIMENTO AUTOMÁTICO DO MAPA")
    logger.info("=" * 60)
    
    driver = configurar_driver()
    if not driver:
        return False
    
    try:
        # 1. Navegar e fazer busca inicial
        logger.info("1️⃣ Configurando busca inicial...")
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        # Buscar localização e palavra-chave
        logic_bot.buscar_cep(driver, "centro, São Paulo (SP)")
        logic_bot.buscar_palavra_chave(driver, "tecnologia")
        
        # 2. Contar elementos iniciais
        elementos_iniciais = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        count_inicial = len(elementos_iniciais)
        logger.info(f"2️⃣ Elementos iniciais encontrados: {count_inicial}")
        
        if count_inicial == 0:
            logger.error("❌ Nenhum elemento inicial encontrado. Teste abortado.")
            return False
        
        # 3. Testar movimento em diferentes direções
        logger.info("3️⃣ Testando movimento em diferentes direções...")
        
        direcoes_teste = ["leste", "sul", "oeste", "norte", "sudeste", "nordeste"]
        resultados_movimento = {}
        
        for direcao in direcoes_teste:
            logger.info(f"🧭 Testando movimento para {direcao}...")
            
            # Contar elementos antes do movimento
            elementos_antes = len(driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]'))
            
            # Executar movimento
            sucesso = logic_bot.mover_mapa_automaticamente(driver, direcao)
            
            # Contar elementos depois do movimento
            time.sleep(2)
            elementos_depois = len(driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]'))
            
            # Registrar resultado
            resultado = {
                'sucesso': sucesso,
                'elementos_antes': elementos_antes,
                'elementos_depois': elementos_depois,
                'novos_elementos': elementos_depois - elementos_antes
            }
            resultados_movimento[direcao] = resultado
            
            if sucesso:
                logger.info(f"✅ {direcao}: {resultado['novos_elementos']} novos elementos carregados")
            else:
                logger.info(f"❌ {direcao}: Nenhum novo elemento carregado")
            
            time.sleep(2)  # Pausa entre testes
        
        # 4. Testar movimento "random"
        logger.info("4️⃣ Testando movimento aleatório...")
        elementos_antes_random = len(driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]'))
        sucesso_random = logic_bot.mover_mapa_automaticamente(driver, "random")
        elementos_depois_random = len(driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]'))
        
        resultados_movimento["random"] = {
            'sucesso': sucesso_random,
            'elementos_antes': elementos_antes_random,
            'elementos_depois': elementos_depois_random,
            'novos_elementos': elementos_depois_random - elementos_antes_random
        }
        
        # 5. Testar integração com estratégias alternativas
        logger.info("5️⃣ Testando integração com estratégias alternativas...")
        elementos_antes_estrategias = len(driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]'))
        sucesso_estrategias = logic_bot.tentar_carregar_mais_resultados(driver)
        elementos_depois_estrategias = len(driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]'))
        
        # 6. Relatório final
        logger.info("=" * 60)
        logger.info("📊 RELATÓRIO FINAL DO TESTE DE MOVIMENTO")
        logger.info("=" * 60)
        
        movimentos_bem_sucedidos = 0
        total_novos_elementos = 0
        
        for direcao, resultado in resultados_movimento.items():
            status = "✅ SUCESSO" if resultado['sucesso'] else "❌ FALHOU"
            novos = resultado['novos_elementos']
            logger.info(f"{direcao.upper()}: {status} - {novos} novos elementos")
            
            if resultado['sucesso']:
                movimentos_bem_sucedidos += 1
                total_novos_elementos += novos
        
        logger.info(f"\n📈 ESTATÍSTICAS:")
        logger.info(f"Movimentos bem-sucedidos: {movimentos_bem_sucedidos}/{len(resultados_movimento)}")
        logger.info(f"Total de novos elementos carregados: {total_novos_elementos}")
        logger.info(f"Elementos iniciais: {count_inicial}")
        logger.info(f"Elementos finais: {elementos_depois_estrategias}")
        
        # Determinar sucesso do teste
        if movimentos_bem_sucedidos > 0:
            logger.info("\n🎉 TESTE BEM-SUCEDIDO!")
            logger.info("✅ A funcionalidade de movimento automático está funcionando")
            return True
        else:
            logger.info("\n⚠️ TESTE PARCIALMENTE BEM-SUCEDIDO")
            logger.info("❌ Nenhum movimento carregou novos elementos")
            logger.info("💡 Isso pode ser normal se a área já foi totalmente explorada")
            return True  # Considerar sucesso se não houve erros críticos
            
    except Exception as e:
        logger.error(f"❌ ERRO CRÍTICO no teste: {str(e)}")
        return False
    finally:
        try:
            driver.quit()
            logger.info("🔒 Driver fechado")
        except:
            pass

def testar_cenario_sem_elementos():
    """Testa o comportamento quando não há elementos na página"""
    logger.info("🧪 TESTANDO CENÁRIO SEM ELEMENTOS")
    
    driver = configurar_driver()
    if not driver:
        return False
    
    try:
        # Navegar para uma busca que provavelmente não terá resultados
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        logic_bot.buscar_cep(driver, "meio do oceano atlântico")
        logic_bot.buscar_palavra_chave(driver, "loja de unicórnios")
        
        # Tentar movimento automático
        sucesso = logic_bot.mover_mapa_automaticamente(driver, "random")
        
        if not sucesso:
            logger.info("✅ Comportamento correto: movimento falhou quando não há elementos")
            return True
        else:
            logger.warning("⚠️ Movimento reportou sucesso mesmo sem elementos")
            return True
            
    except Exception as e:
        logger.error(f"❌ Erro no teste sem elementos: {str(e)}")
        return False
    finally:
        try:
            driver.quit()
        except:
            pass

def main():
    """Executa todos os testes de movimento automático"""
    logger.info("🚀 INICIANDO TESTES DE MOVIMENTO AUTOMÁTICO DO MAPA")
    logger.info("=" * 80)
    
    resultados = {}
    
    # Teste 1: Movimento automático normal
    logger.info("\n🧪 TESTE 1: Movimento Automático Normal")
    resultados['movimento_normal'] = testar_movimento_automatico()
    
    # Teste 2: Cenário sem elementos
    logger.info("\n🧪 TESTE 2: Cenário Sem Elementos")
    resultados['sem_elementos'] = testar_cenario_sem_elementos()
    
    # Resumo final
    logger.info("\n" + "=" * 80)
    logger.info("📊 RESUMO FINAL DOS TESTES")
    logger.info("=" * 80)
    
    sucessos = 0
    total = len(resultados)
    
    for teste, sucesso in resultados.items():
        status = "✅ PASSOU" if sucesso else "❌ FALHOU"
        logger.info(f"{teste.upper()}: {status}")
        if sucesso:
            sucessos += 1
    
    logger.info(f"\n🎯 RESULTADO: {sucessos}/{total} testes passaram")
    
    if sucessos == total:
        logger.info("🎉 TODOS OS TESTES PASSARAM!")
        logger.info("✅ A funcionalidade de movimento automático está funcionando perfeitamente")
    elif sucessos >= total * 0.5:
        logger.info("⚠️ A maioria dos testes passou")
        logger.info("💡 A funcionalidade está funcionando, mas pode precisar de ajustes")
    else:
        logger.info("❌ Muitos testes falharam")
        logger.info("🔧 A funcionalidade precisa ser revisada")
    
    return sucessos == total

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
